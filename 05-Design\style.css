/*
SPACING SYSTEM (px)
2 / 4 / 8 / 12 / 16 / 24 / 32 / 48 / 64 / 80 / 96 / 128

FONT SIZE SYSTEM (px)
10 / 12 / 14 / 16 / 18 / 20 / 24 / 30 / 36 / 44 / 52 / 62 / 74 / 86 / 98
*/

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ------------------------ */
/* GENERAL STYLES */
/* ------------------------ */
body {
  font-family: sans-serif;
}

.container {
  width: 960px;
  margin: 0 auto;
}

header,
section {
  margin-bottom: 48px;
}

h2 {
  margin-bottom: 48px;
}

.grid-3-cols {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 80px;
}

/* ------------------------ */
/* COMPONENT STYLES */
/* ------------------------ */

/* HEADER */
header {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 80px;
  margin-top: 48px;
}

.header-text-box {
  align-self: center;
}

h1 {
  margin-bottom: 24px;
}

.header-text {
  margin-bottom: 24px;
}

img {
  width: 100%;
}

/* FEATURES */
.features-icon {
}

.features-title {
  margin-bottom: 16px;
}

.features-text {
}

/* TESTIMONIAL */
.testimonial-section {
}

.testimonial-box {
  grid-column: 2 / -1;
  align-self: center;
}

.testimonial-box h2 {
  margin-bottom: 24px;
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 24px;
}

/* CHAIRS */
.chair-box {
  padding: 24px;
}

h3 {
  margin-bottom: 24px;
}

.chair-details {
  list-style: none;
  margin-bottom: 24px;
}

.chair-details li {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.chair-details li:last-child {
  margin-bottom: 0;
}

.chair-icon {
}

.chair-price {
  display: flex;
  justify-content: space-between;
}

footer {
  margin-bottom: 48px;
}

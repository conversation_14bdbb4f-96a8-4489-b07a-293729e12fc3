<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vue</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  </head>
  <body>
    <div id="app">
      <h1>To List</h1>
      <input type="text" v-model="enterValue" />
      <button v-on:click="addGoal" placeholder="请输入你的目标">添加</button>
      <ul>
        <li v-for="goal in goals">{{goal}}</li>
      </ul>
    </div>
  </body>
  <script>
    Vue.createApp({
      data() {
        return {
          enterValue: "",
          goals: [],
        };
      },
      methods: {
        addGoal() {
          this.goals.push(this.enterValue);
          this.enterValue = "";
        },
      },
    }).mount("#app");
  </script>
</html>

* {
  /* border-top: 10px solid #1098ad; */
  margin: 0;
  padding: 0;
}

/* PAGE SECTIONS */
body {
  color: #444;
  font-family: sans-serif;

  border-top: 10px solid #1098ad;
  position: relative;
}

.container {
  width: 800px;
  /* margin-left: auto;
  margin-right: auto; */
  margin: 0 auto;
}

.main-header {
  background-color: #f7f7f7;
  /* padding: 20px;
  padding-left: 40px;
  padding-right: 40px; */
  padding: 20px 40px;
  margin-bottom: 60px;
  /* height: 80px; */
}

nav {
  font-size: 18px;
  /* text-align: center; */
}

article {
  margin-bottom: 60px;
}

.post-header {
  margin-bottom: 40px;
  /* position: relative; */
}

aside {
  background-color: #f7f7f7;
  border-top: 5px solid #1098ad;
  border-bottom: 5px solid #1098ad;
  /* padding-top: 50px;
  padding-bottom: 50px; */
  padding: 50px 0;
  width: 500px;
}

/* SMALLER ELEMENTS */
h1,
h2,
h3 {
  color: #1098ad;
}

h1 {
  font-size: 26px;
  text-transform: uppercase;
  font-style: italic;
}

h2 {
  font-size: 40px;
  margin-bottom: 30px;
}

h3 {
  font-size: 30px;
  margin-bottom: 20px;
  margin-top: 40px;
}

h4 {
  font-size: 20px;
  text-transform: uppercase;
  text-align: center;
}

p {
  font-size: 22px;
  line-height: 1.5;
  margin-bottom: 15px;
}

ul,
ol {
  margin-left: 50px;
  margin-bottom: 20px;
}

li {
  font-size: 20px;
  margin-bottom: 10px;
  /* display: inline; */
}

li:last-child {
  margin-bottom: 0;
}

/* footer p {
  font-size: 16px;
} */

/* article header p {
  font-style: italic;
} */

#author {
  font-style: italic;
  font-size: 18px;
}

#copyright {
  font-size: 16px;
}

.related-author {
  font-size: 18px;
  font-weight: bold;
}

/* ul {
  list-style: none;
} */

.related {
  list-style: none;
}

body {
  /* background-color: orangered; */
}

/* .first-li {
  font-weight: bold;
} */

li:first-child {
  font-weight: bold;
}

li:last-child {
  font-style: italic;
}

li:nth-child(even) {
  /* color: red; */
}

/* Misconception: this won't work! */
article p:first-child {
  color: red;
}

/* Styling links */
a:link {
  color: #1098ad;
  text-decoration: none;
}

a:visited {
  /* color: #777; */
  color: #1098ad;
}

a:hover {
  color: orangered;
  font-weight: bold;
  text-decoration: underline orangered;
}

a:active {
  background-color: black;
  font-style: italic;
}
/* LVHA */

.post-img {
  width: 100%;
  height: auto;
}

nav a:link {
  /* background-color: orangered;
  margin: 20px;
  padding: 20px;

  display: block; */

  margin-right: 30px;
  margin-top: 10px;
  display: inline-block;
}

nav a:link:last-child {
  margin-right: 0;
}

button {
  font-size: 22px;
  padding: 20px;
  cursor: pointer;

  position: absolute;
  /* top: 50px;
  left: 50px; */
  bottom: 50px;
  right: 50px;
}

h1::first-letter {
  font-style: normal;
  margin-right: 5px;
}

h3 + p::first-line {
  /* color: red; */
}

h2 {
  /* background-color: orange; */
  position: relative;
}

h2::before {
  content: "TOP";
  background-color: #ffe70e;
  color: #444;
  font-size: 16px;
  font-weight: bold;
  display: inline-block;
  padding: 5px 10px;
  position: absolute;
  top: -10px;
  right: -25px;
}

/* Resolving conflicts */
/* #copyright {
  color: red;
}

.copyright {
  color: blue;
}

.text {
  color: yellow;
}

footer p {
  color: green !important;
} */

/* nav a:link,
nav p {
  font-size: 18px;
} */
